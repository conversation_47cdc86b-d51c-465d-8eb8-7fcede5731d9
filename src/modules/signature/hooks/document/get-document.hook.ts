import { useQuery } from "@tanstack/react-query";
import { useAtomValue, useSetAtom } from "jotai";
import { useEffect, useMemo } from "react";
import { SignatureQueryKeys } from "../../query-keys";
import { DocumentManageState } from "../../services/manage/manage-state-document";
import { documentHashCode } from "../../states/document/document-hash.state";
import { signaturePositionAtom } from "../../states/signature/signature-position.state";
import { IGetDocument } from "../../types/document/get-document.type";

export const useGetDocument = () => {
	const documentToken = useAtomValue(documentHashCode);
	const setSignaturePosition = useSetAtom(signaturePositionAtom);
	const getCurrentStateDocument = useMemo(() => new DocumentManageState(), []);

	const {
		data: documentData,
		isLoading,
		error,
		isFetching,
	} = useQuery({
		queryKey: SignatureQueryKeys.documentInfo(documentToken!),
		queryFn: () => getCurrentStateDocument.getDocument(documentToken!),
		enabled: !!documentToken,
		retry: false,
	});

	// Define a posição da assinatura quando o documento é carregado
	useEffect(() => {
		if (documentData?.data && !documentData.isDocumentSigned) {
			const document = documentData.data as IGetDocument;
			if (document.rubric) {
				console.log("📍 Definindo posição da assinatura do documento:", document.rubric);
				setSignaturePosition({
					x: document.rubric.coordinates.x,
					y: document.rubric.coordinates.y,
					page: document.rubric.pageIndex,
					scale: 0.5, // Escala padrão
				});
			}
		}
	}, [documentData, setSignaturePosition]);

	return {
		isLoading,
		isFetching,
		documentData,
		error,
	};
};

/**
 * Interface para definir overlays que podem ser renderizados sobre o PDF
 * Segue o princípio Open/Closed - extensível para novos tipos de overlay
 */
export interface IPdfOverlay {
	/**
	 * Identificador único do overlay
	 */
	id: string;
	
	/**
	 * P<PERSON>gina onde o overlay deve ser renderizado (0-indexed)
	 */
	page: number;
	
	/**
	 * Posição X do overlay na página
	 */
	x: number;
	
	/**
	 * Posição Y do overlay na página
	 */
	y: number;
	
	/**
	 * Escala do overlay
	 */
	scale: number;
	
	/**
	 * Tipo do overlay para identificação
	 */
	type: string;
}

/**
 * Interface para renderizadores de overlay
 * Implementa Strategy Pattern para diferentes tipos de renderização
 */
export interface IPdfOverlayRenderer {
	/**
	 * Tipo de overlay que este renderizador suporta
	 */
	supportedType: string;
	
	/**
	 * Renderiza o overlay no contexto do canvas
	 */
	render(
		context: CanvasRenderingContext2D,
		overlay: IPdfOverlay,
		scale: number,
		data?: unknown
	): Promise<void>;
}

/**
 * Interface para gerenciamento de overlays no PDF
 */
export interface IPdfOverlayManager {
	/**
	 * Adiciona um overlay à página especificada
	 */
	addOverlay(overlay: IPdfOverlay): void;
	
	/**
	 * Remove um overlay pelo ID
	 */
	removeOverlay(id: string): void;
	
	/**
	 * Obtém todos os overlays de uma página específica
	 */
	getOverlaysForPage(page: number): IPdfOverlay[];
	
	/**
	 * Registra um renderizador para um tipo específico de overlay
	 */
	registerRenderer(renderer: IPdfOverlayRenderer): void;
	
	/**
	 * Remove o registro de um renderizador
	 */
	unregisterRenderer(type: string): void;
}

/**
 * Props para componentes que suportam overlays
 */
export interface IPdfWithOverlaysProps {
	/**
	 * Lista de overlays a serem renderizados
	 */
	overlays?: IPdfOverlay[];
	
	/**
	 * Dados adicionais para renderização dos overlays
	 */
	overlayData?: Record<string, unknown>;
	
	/**
	 * Callback chamado quando um overlay é clicado
	 */
	onOverlayClick?: (overlay: IPdfOverlay) => void;
}

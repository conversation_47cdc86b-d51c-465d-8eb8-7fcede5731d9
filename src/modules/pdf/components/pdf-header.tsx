import { Download, Minus, Plus, Printer } from "lucide-react";
import React, { useCallback, memo } from "react";

interface PdfViewerHeaderProps {
	currentPage: number;
	totalPages: number;
	zoom: number;
	maxZoom: number;
	onIncreaseZoom: () => void;
	onDecreaseZoom: () => void;
	onPrint: () => void;
	onDownload: () => void;
}

const PdfViewerHeader: React.FC<PdfViewerHeaderProps> = memo(
	({ currentPage, totalPages, zoom, onIncreaseZoom, onDecreaseZoom, onPrint, onDownload, maxZoom }) => {
		const handleZoomDecrease = useCallback(
			(e: React.MouseEvent) => {
				e.preventDefault();
				onDecreaseZoom();
			},
			[onDecreaseZoom]
		);

		const handleZoomIncrease = useCallback(
			(e: React.MouseEvent) => {
				e.preventDefault();
				onIncreaseZoom();
			},
			[onIncreaseZoom]
		);

		const handlePrint = useCallback(
			(e: React.MouseEvent) => {
				e.preventDefault();
				onPrint();
			},
			[onPrint]
		);

		const handleDownload = useCallback(
			(e: React.MouseEvent) => {
				e.preventDefault();
				onDownload();
			},
			[onDownload]
		);

		const zoomPercentage = `${(zoom * 100).toFixed(0)}%`;
		const isMaxZoom = zoom >= maxZoom;
		return (
			<div className="sticky top-0 z-50 hidden lg:flex flex-col lg:flex-row items-center justify-between bg-gradient-to-r from-gray-800 to-gray-700 text-white p-3 shadow-lg">
				<div className="w-1/3 flex" />

				<div className="flex items-center justify-center gap-6">
					<div className="flex items-center gap-2">
						<span className="font-bold text-xl">{currentPage}</span>
						<span className="text-sm">/ {totalPages}</span>
					</div>

					<div className="flex items-center gap-3 bg-gray-900 px-3 py-1 rounded">
						<button
							type="button"
							className="p-2 hover:bg-gray-700 transition rounded text-2xl disabled:opacity-50"
							onClick={handleZoomDecrease}
							aria-label="Diminuir zoom"
						>
							<Minus />
						</button>
						<span className="font-semibold min-w-[3rem] text-center">{zoomPercentage}</span>
						<button
							type="button"
							className="p-2 hover:bg-gray-700 transition rounded text-2xl disabled:text-gray-500 disabled:opacity-50"
							onClick={handleZoomIncrease}
							disabled={isMaxZoom}
							aria-label="Aumentar zoom"
						>
							<Plus />
						</button>
					</div>
				</div>

				<div className="flex items-center justify-end w-1/3 gap-4 mt-2 lg:mt-0">
					<button
						type="button"
						className="flex items-center gap-2 bg-gray-900 hover:bg-gray-700 transition px-3 py-2 rounded-lg font-bold disabled:opacity-50"
						onClick={handlePrint}
						aria-label="Imprimir documento"
					>
						<Printer />
					</button>
					<button
						type="button"
						className="flex items-center gap-2 bg-gray-900 hover:bg-gray-700 transition px-3 py-2 rounded-lg font-bold disabled:opacity-50"
						onClick={handleDownload}
						aria-label="Baixar documento"
					>
						<Download />
					</button>
				</div>
			</div>
		);
	}
);

PdfViewerHeader.displayName = "PdfViewerHeader";

export default PdfViewerHeader;

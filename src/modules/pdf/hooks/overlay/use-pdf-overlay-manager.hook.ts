import { useCallback, useMemo } from "react";
import { useAtom } from "jotai";
import { IPdfOverlay, IPdfOverlayRenderer } from "../../types/pdf-overlay.interface";
import { pdfOverlaysAtom, pdfRenderersAtom } from "../../states";

/**
 * Tipo estendido do overlay manager com método getRenderer
 */
type ExtendedOverlayManager = {
	addOverlay: (overlay: IPdfOverlay) => void;
	removeOverlay: (id: string) => void;
	getOverlaysForPage: (page: number) => IPdfOverlay[];
	registerRenderer: (renderer: IPdfOverlayRenderer) => void;
	unregisterRenderer: (type: string) => void;
	getRenderer: (type: string) => IPdfOverlayRenderer | undefined;
};

/**
 * Hook para gerenciar overlays no PDF
 * Implementa o padrão Strategy para renderização extensível
 * Segue o princípio da responsabilidade única
 */
export const usePdfOverlayManager = () => {
	const [overlays, setOverlays] = useAtom(pdfOverlaysAtom);
	const [renderers, setRenderers] = useAtom(pdfRenderersAtom);

	const addOverlay = useCallback(
		(overlay: IPdfOverlay) => {
			setOverlays(currentOverlays => {
				const newOverlays = new Map(currentOverlays);
				const pageOverlays = newOverlays.get(overlay.page) || [];
				const existingIndex = pageOverlays.findIndex(o => o.id === overlay.id);

				if (existingIndex >= 0) {
					pageOverlays[existingIndex] = overlay;
				} else {
					pageOverlays.push(overlay);
				}

				newOverlays.set(overlay.page, pageOverlays);
				return newOverlays;
			});
		},
		[setOverlays]
	);

	const removeOverlay = useCallback(
		(id: string) => {
			setOverlays(currentOverlays => {
				const newOverlays = new Map(currentOverlays);
				let hasChanges = false;

				newOverlays.forEach((pageOverlays, page) => {
					const filteredOverlays = pageOverlays.filter(overlay => overlay.id !== id);
					if (filteredOverlays.length !== pageOverlays.length) {
						newOverlays.set(page, filteredOverlays);
						hasChanges = true;
					}
				});

				return hasChanges ? newOverlays : currentOverlays;
			});
		},
		[setOverlays]
	);

	const getOverlaysForPage = useCallback(
		(page: number): IPdfOverlay[] => {
			return overlays.get(page) || [];
		},
		[overlays]
	);

	const registerRenderer = useCallback(
		(renderer: IPdfOverlayRenderer) => {
			console.log("🔧 Registrando renderizador", renderer.supportedType);
			setRenderers(currentRenderers => {
				const newRenderers = new Map(currentRenderers);
				newRenderers.set(renderer.supportedType, renderer);
				return newRenderers;
			});
		},
		[setRenderers]
	);

	const unregisterRenderer = useCallback(
		(type: string) => {
			setRenderers(currentRenderers => {
				const newRenderers = new Map(currentRenderers);
				newRenderers.delete(type);
				return newRenderers;
			});
		},
		[setRenderers]
	);

	// Memoriza o getRenderer com dependência estável
	const getRenderer = useCallback(
		(type: string) => {
			const renderer = renderers.get(type);
			console.log(`🔍 Buscando renderizador para tipo '${type}':`, renderer);
			console.log("🗂️ Renderizadores disponíveis:", Array.from(renderers.keys()));
			return renderer;
		},
		[renderers]
	);

	// Memoriza o objeto retornado apenas com as dependências essenciais
	return useMemo(
		() => ({
			addOverlay,
			removeOverlay,
			getOverlaysForPage,
			registerRenderer,
			unregisterRenderer,
			getRenderer,
		}),
		[addOverlay, removeOverlay, getOverlaysForPage, registerRenderer, unregisterRenderer, getRenderer]
	);
};

/**
 * Hook para renderizar overlays em uma página específica
 * Responsabilidade única: renderização de overlays
 */
export const useRenderOverlays = (overlayManager: ExtendedOverlayManager) => {
	const renderOverlaysForPage = useCallback(
		async (context: CanvasRenderingContext2D, page: number, scale: number, overlayData?: Record<string, unknown>) => {
			const overlays = overlayManager.getOverlaysForPage(page);
			console.log("🎨 Renderizando overlays para página", page, "com escala", scale, "dados", overlayData);

			for (const overlay of overlays) {
				console.log("🖼️ Renderizando overlay", overlay);
				const renderer = overlayManager.getRenderer(overlay.type);
				console.log("🔧 Renderizador encontrado:", renderer);
				if (renderer) {
					try {
						await renderer.render(context, overlay, scale, overlayData?.[overlay.type]);
					} catch (error) {
						console.error(`❌ Erro ao renderizar overlay ${overlay.id} do tipo ${overlay.type}:`, error);
					}
				} else {
					console.warn(`⚠️ Nenhum renderizador encontrado para tipo: ${overlay.type}`);
				}
			}
		},
		[overlayManager]
	);

	return useMemo(
		() => ({
			renderOverlaysForPage,
		}),
		[renderOverlaysForPage]
	);
};

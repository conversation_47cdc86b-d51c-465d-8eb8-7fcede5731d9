import { useCallback, useMemo, useEffect, useRef } from "react";
import { IPdfOverlay, IPdfWithOverlaysProps } from "../../types/pdf-overlay.interface";
import { usePdfOverlayManager, useRenderOverlays } from "./use-pdf-overlay-manager.hook";

/**
 * Hook principal para integração de overlays com PDF
 * Responsabilidade única: coordenar overlays no PDF
 * Segue o princípio da inversão de dependência
 */
export const usePdfWithOverlays = (props?: IPdfWithOverlaysProps) => {
	const overlayManager = usePdfOverlayManager();
	const { renderOverlaysForPage } = useRenderOverlays(overlayManager);
	const lastSyncedOverlaysRef = useRef<IPdfOverlay[]>([]);

	useEffect(() => {
		if (!props?.overlays) return;

		const currentOverlays = props.overlays;
		const lastOverlays = lastSyncedOverlaysRef.current;

		const hasChanged =
			currentOverlays.length !== lastOverlays.length ||
			currentOverlays.some((overlay, index) => {
				const lastOverlay = lastOverlays[index];
				return (
					!lastOverlay ||
					overlay.id !== lastOverlay.id ||
					overlay.page !== lastOverlay.page ||
					overlay.x !== lastOverlay.x ||
					overlay.y !== lastOverlay.y ||
					overlay.type !== lastOverlay.type
				);
			});

		if (hasChanged) {
			console.log("📋 Sincronizando overlays externos - mudanças detectadas");
			currentOverlays.forEach(overlay => {
				// console.log("adicionando overlay", overlay);
				overlayManager.addOverlay(overlay);
			});
			lastSyncedOverlaysRef.current = [...currentOverlays];
		}
	}, [props?.overlays, overlayManager]);

	const handleRenderOverlays = useCallback(
		async (context: CanvasRenderingContext2D, page: number, scale: number, overlayData?: Record<string, unknown>) => {
			const combinedOverlayData = {
				...overlayData,
				...props?.overlayData,
			};
			await renderOverlaysForPage(context, page, scale, combinedOverlayData);
		},
		[renderOverlaysForPage, props?.overlayData]
	);

	const handleOverlayClick = useCallback(
		(overlay: IPdfOverlay) => {
			props?.onOverlayClick?.(overlay);
		},
		[props]
	);

	const pdfProps = useMemo(
		() => ({
			overlays: props?.overlays,
			overlayData: props?.overlayData,
			onRenderOverlays: handleRenderOverlays,
		}),
		[props?.overlays, props?.overlayData, handleRenderOverlays]
	);

	return {
		overlayManager,
		handleOverlayClick,
		pdfProps,
	};
};

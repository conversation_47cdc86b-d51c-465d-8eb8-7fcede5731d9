import { useCallback } from "react";

/**
 * Interface genérica para posição de scroll
 * Não depende mais de tipos específicos de assinatura
 */
export interface IScrollPosition {
	x: number;
	y: number;
	page: number;
}

/**
 * Hook genérico para scroll para posição específica no PDF
 * Responsabilidade única: scroll no PDF
 * Não mais acoplado com módulo de assinatura
 */
export function useScrollToPosition(containerRef: React.RefObject<HTMLElement | null>, zoom: number): (position: IScrollPosition | null) => void {
	const scrollToPosition = useCallback(
		(position: IScrollPosition | null): void => {
			if (!position) return;
			const container = containerRef.current;
			if (!container) return;

			const { page, x, y } = position;
			const canvasSelector = `canvas[data-page="${page + 1}"]`;
			const canvas = container.querySelector(canvasSelector) as HTMLCanvasElement | null;
			if (!canvas) return;

			const targetX = canvas.offsetLeft + x * zoom;
			const targetY = canvas.offsetTop + y * zoom;

			const { width: containerWidth, height: containerHeight } = container.getBoundingClientRect();
			const scrollLeft = targetX - containerWidth / 2;
			const scrollTop = targetY - containerHeight / 2;

			container.scrollTo({
				left: scrollLeft,
				top: scrollTop,
				behavior: "smooth",
			});
		},
		[containerRef, zoom]
	);

	return scrollToPosition;
}

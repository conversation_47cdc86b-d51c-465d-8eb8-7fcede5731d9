import { useAtom, useAtomValue } from "jotai";
import { useCallback } from "react";
import { documentCurrentPage } from "../../states/current-page-document.state";
import { isFullPdfLoadedAtom } from "../../states/is-full-loaded.state";
import { pdfDocumentProxy } from "../../states/pdf-proxy.state";

/**
 * Hook para gerenciar estados do PDF de forma centralizada
 * Responsabilidade única: coordenar estados do PDF
 * Facilita manutenção e testes
 */
export const usePdfStateManager = () => {
	const pdfDocument = useAtomValue(pdfDocumentProxy);
	const currentPage = useAtomValue(documentCurrentPage);
	const [isFullyLoaded, setIsFullyLoaded] = useAtom(isFullPdfLoadedAtom);

	/**
	 * Marca o PDF como totalmente carregado
	 */
	const markAsFullyLoaded = useCallback(() => {
		setIsFullyLoaded(true);
	}, [setIsFullyLoaded]);

	/**
	 * Reseta o estado de carregamento
	 */
	const resetLoadingState = useCallback(() => {
		setIsFullyLoaded(false);
	}, [setIsFullyLoaded]);

	/**
	 * Obtém informações básicas do documento
	 */
	const getDocumentInfo = useCallback(() => {
		if (!pdfDocument) return null;

		return {
			totalPages: pdfDocument.numPages,
			currentPage,
			isFullyLoaded,
		};
	}, [pdfDocument, currentPage, isFullyLoaded]);

	/**
	 * Verifica se o PDF está pronto para uso
	 */
	const isPdfReady = useCallback(() => {
		return pdfDocument !== null && isFullyLoaded;
	}, [pdfDocument, isFullyLoaded]);

	return {
		// Estados
		pdfDocument,
		currentPage,
		isFullyLoaded,

		// Ações
		markAsFullyLoaded,
		resetLoadingState,

		// Utilitários
		getDocumentInfo,
		isPdfReady,
	};
};

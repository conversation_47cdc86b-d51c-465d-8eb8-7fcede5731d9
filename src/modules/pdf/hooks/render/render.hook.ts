import { useDebounce } from "@/shared/hooks/utils/debounce";
import { RefObject, useCallback, useEffect, useLayoutEffect, useRef } from "react";
import { IPdfOverlay } from "../../types/pdf-overlay.interface";
import { useVisibility } from "../visual-interactions/use-canva-visible.hook";

// Tipos importados dinamicamente para evitar problemas de SSR
type PDFDocumentProxy = any;
type PDFPageProxy = any;

export interface IUsePdfRenderer {
	pdfDocument: PDFDocumentProxy;
	pageNumber: number;
	zoom: number;
	canvasRef: React.RefObject<HTMLCanvasElement | null>;
	onRenderComplete?: () => void;
	overlays?: IPdfOverlay[];
	overlayData?: Record<string, unknown>;
	forceRenderAllPages?: boolean;
	onRenderOverlays?: (context: CanvasRenderingContext2D, page: number, scale: number, overlayData?: Record<string, unknown>) => Promise<void>;
}

export const usePdfRenderer = ({
	pdfDocument,
	pageNumber,
	zoom,
	canvasRef,
	onRenderComplete,
	overlayData,
	forceRenderAllPages,
	onRenderOverlays,
}: Omit<IUsePdfRenderer, "overlays">) => {
	const renderTaskRef = useRef<ReturnType<PDFPageProxy["render"]> | null>(null);
	const isRenderingOverlaysRef = useRef(false);
	const lastRenderedPageRef = useRef<number | null>(null);
	const lastRenderedZoomRef = useRef<number | null>(null);
	const isVisible = useVisibility(canvasRef as RefObject<HTMLElement>, 0.1);

	const cancelRenderTask = () => {
		if (renderTaskRef.current) {
			renderTaskRef.current.cancel();
			renderTaskRef.current = null;
		}
	};

	const renderPage = useCallback(async () => {
		if (!canvasRef.current) return;

		const controller = new AbortController();
		const { signal } = controller;

		try {
			const page = await pdfDocument.getPage(pageNumber);

			if (signal.aborted) return;

			const baseViewport = page.getViewport({ scale: 1 });
			const deviceScale = window.devicePixelRatio || 1;
			const scale = zoom * deviceScale;
			const viewport = page.getViewport({ scale });
			const canvas = canvasRef.current;
			const context = canvas.getContext("2d");
			if (!context) return;

			canvas.width = viewport.width;
			canvas.height = viewport.height;

			if (window.matchMedia("(max-width: 768px)").matches) {
				const containerWidth = canvas.parentElement?.clientWidth || window.innerWidth;
				const aspectRatio = baseViewport.height / baseViewport.width;
				canvas.style.width = "100%";
				canvas.style.height = `${containerWidth * aspectRatio}px`;
			} else {
				canvas.style.width = `${baseViewport.width * zoom}px`;
				canvas.style.height = `${baseViewport.height * zoom}px`;
			}

			if (renderTaskRef.current) renderTaskRef.current.cancel();
			cancelRenderTask();
			const renderTask = page.render({ canvasContext: context, viewport });
			renderTaskRef.current = renderTask;

			await renderTask.promise;

			// // Renderiza overlays se disponível
			// if (onRenderOverlays && !isRenderingOverlaysRef.current) {
			// 	isRenderingOverlaysRef.current = true;
			// 	try {
			// 		console.log("Renderizando overlays", overlayData);
			// 		await onRenderOverlays(context, pageNumber - 1, scale, overlayData);
			// 	} catch (error) {
			// 		console.error("Erro ao renderizar overlays:", error);
			// 	} finally {
			// 		isRenderingOverlaysRef.current = false;
			// 	}
			// }

			if (onRenderComplete) onRenderComplete();
		} catch (error: unknown) {
			if (error instanceof Error) {
				if (error.name === "RenderingCancelledException" || error.message.includes("Rendering cancelled")) return;
			}
			console.error("Erro ao renderizar a página", error);
		}
	}, [pdfDocument, pageNumber, zoom, onRenderComplete, canvasRef]);

	// UseEffect separado para renderização de overlays - SEM debounce para evitar perda durante scroll
	useEffect(() => {
		const renderOverlays = async () => {
			if (!onRenderOverlays || !canvasRef.current || isRenderingOverlaysRef.current) return;

			console.log("🎨 [OVERLAY DEBUG] Tentando renderizar overlays", {
				pageNumber,
				zoom,
				lastRenderedPage: lastRenderedPageRef.current,
				lastRenderedZoom: lastRenderedZoomRef.current,
				overlayData: overlayData ? "presente" : "ausente",
			});

			// Verifica se a página foi renderizada (mais flexível para permitir re-renderização durante scroll)
			if (lastRenderedPageRef.current !== pageNumber) {
				console.log("⏳ [OVERLAY DEBUG] Página ainda não renderizada, aguardando...");
				return;
			}

			const canvas = canvasRef.current;
			const context = canvas.getContext("2d");
			if (!context) {
				console.log("❌ [OVERLAY DEBUG] Context não disponível");
				return;
			}

			const deviceScale = window.devicePixelRatio || 1;
			const scale = zoom * deviceScale;

			isRenderingOverlaysRef.current = true;
			try {
				console.log("🎨 [OVERLAY DEBUG] Iniciando renderização de overlays", { scale, pageNumber });
				await onRenderOverlays(context, pageNumber - 1, scale, overlayData);
				console.log("✅ [OVERLAY DEBUG] Overlays renderizados com sucesso");
			} catch (error) {
				console.error("❌ [OVERLAY DEBUG] Erro ao renderizar overlays:", error);
			} finally {
				isRenderingOverlaysRef.current = false;
			}
		};

		// Renderização imediata para evitar perda durante scroll
		renderOverlays();
	}, [onRenderOverlays, overlayData, pageNumber, zoom, canvasRef]);

	useLayoutEffect(() => {
		if (forceRenderAllPages || (isVisible && canvasRef.current && canvasRef.current.clientWidth > 0 && canvasRef.current.clientHeight > 0)) {
			renderPage().then(() => {
				// Marca que a página foi renderizada com sucesso
				lastRenderedPageRef.current = pageNumber;
				lastRenderedZoomRef.current = zoom;
			});
		}
	}, [forceRenderAllPages, renderPage, canvasRef, isVisible, pageNumber, zoom]);

	const debouncedRenderPage = useDebounce(renderPage, 200, [renderPage]);
	useEffect(() => {
		window.addEventListener("resize", debouncedRenderPage);
		return () => {
			window.removeEventListener("resize", debouncedRenderPage);
		};
	}, [debouncedRenderPage]);
};

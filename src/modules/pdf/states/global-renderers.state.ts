import { atom } from "jotai";
import { IPdfOverlayRenderer } from "../types/pdf-overlay.interface";

/**
 * Atom global para manter uma única instância de cada tipo de renderizador
 * Previne registros duplicados e loops infinitos
 */
export const globalRenderersAtom = atom<Map<string, IPdfOverlayRenderer>>(new Map());

/**
 * Atom derivado para verificar se um renderizador específico está registrado
 */
export const isRendererRegisteredAtom = (type: string) => 
	atom((get) => get(globalRenderersAtom).has(type));

/**
 * Atom derivado para registrar um renderizador globalmente
 */
export const registerRendererAtom = atom(
	null,
	(get, set, renderer: IPdfOverlayRenderer) => {
		const currentRenderers = get(globalRenderersAtom);
		if (!currentRenderers.has(renderer.supportedType)) {
			const newRenderers = new Map(currentRenderers);
			newRenderers.set(renderer.supportedType, renderer);
			set(globalRenderersAtom, newRenderers);
			return true; // Successfully registered
		}
		return false; // Already registered
	}
);
